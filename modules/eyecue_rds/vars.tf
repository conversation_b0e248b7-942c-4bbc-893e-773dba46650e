##############################
# General
##############################
variable "aws_region" {}
variable "aws_account_id" {}
variable "product" {
  type        = string
  description = "Fingermark Product"
  default     = ""
}
variable "vpc_id" {
  type        = string
  description = "Fingermark VPC ID"
  default     = ""
}
variable "tags" {
  description = "Infrastructure Default Tags"
  type        = map(any)
  default = {
    Terraform   = "true"
    Stack       = "network"
    Product     = "eyecue"
    Environment = "prod"
    Squad       = "Platform"
  }
}

##############################
# RDS Instance Settings
##############################
variable "rds_instance_identifier" {
  default = "master-postgres"
}
variable "rds_name" {
  default = "eyeq"
}
variable "rds_username" {
  default = "eyecue"
}
variable "rds_engine" {
  default = "postgres"
}
variable "rds_engine_version" {
  default = "12.17"
}
variable "rds_master_instance_class" {
  default = "db.t3.medium"
}
variable "rds_replica_instance_class" {
  default = "db.t3.micro"
}
variable "rds_allocated_storage" {
  default = 100
}
variable "rds_max_allocated_storage" {
  default = 0
}
variable "rds_master_iops" {
  default = "3000"
  type    = string
}
variable "rds_replica_iops" {
  default = "3000"
  type    = string
}
variable "rds_storage_type" {
  default = "gp3"
  type    = string
}
variable "rds_storage_encrypted" {
  default = true
  type    = bool
}
variable "rds_port" {
  default = 5432
}
variable "rds_backup_retention_period" {
  default = 1
  type    = number
}
variable "rds_backup_window" {
  default = "03:00-06:00"
  type    = string
}
variable "rds_maintenance_window" {
  default = "Mon:00:00-Mon:03:00"
  type    = string
}
variable "rds_performance_insights_enabled" {
  default = false
  type    = bool
}
variable "rds_skip_final_snapshot" {
  default = false
  type    = bool
}
variable "rds_timeout" {
  default = {
    create = "40m"
    delete = "40m"
    update = "80m"
  }
}
variable "rds_master_tags" {
  default = {
    Name        = "master-postgres"
    Terraform   = "true"
    Environment = "prod"
    Stack       = "cv"
    Product     = "Eyecue"
    Squad       = "Platform"
  }
}
variable "rds_replica_tags" {
  default = {
    Name        = "master-postgres"
    Terraform   = "true"
    Environment = "prod"
    Stack       = "cv"
    Product     = "Eyecue"
    Squad       = "Platform"
  }
}
variable "create_random_password" {
  default     = true
  type        = bool
  description = <<-DOC
    Create a random password for the RDS instance.

    If set to true the password is created by this module and not the official rds module.
    https://registry.terraform.io/modules/terraform-aws-modules/rds/aws/5.2.0

    This should be set to false if the rds_create_random_password variable
    is set to true, and vice versa.
  DOC
}
variable "rds_create_random_password" {
  default     = false
  type        = bool
  description = <<-DOC
    Create a random password for the RDS instance.

    This should be set to false if the create_random_password variable
    is set to true, and vice versa.
  DOC
}
variable "rds_create_db_subnet_group" {
  default = "true"
}
variable "rds_apply_changes_immediately" {
  default = false
}
variable "rds_ca_cert_identifier" {}
variable "deletion_protection" {
  default = true
}
variable "special_password" {
  default = true
}
variable "create_replica" {
  default = false
}
variable "allow_major_version_upgrade" {
  description = "Allow major version upgrade"
  type        = bool
  default     = false
}

##############################
# Networking
##############################
variable "subnet_ids" {}
variable "vpc_security_group_ids" {
  type    = list(any)
  default = []
}
variable "vpc_security_group_name" {
  default = "postgressg"
  type    = string
}
variable "vpc_security_group_description" {
  default = null
  type    = string
}
variable "db_subnet_group_use_name_prefix" {
  default = true
  type    = bool
}
variable "db_subnet_group_name" {
  default = null
  type    = string
}

##############################
# Parameter Groups
##############################
variable "create_db_parameter_group" {
  default = false
}
variable "parameter_group_name" {
  default = null
}
variable "parameter_group_family" {
  default = "postgres10"
}
variable "parameter_group_parameters" {
  default = []
}
variable "parameter_group_use_name_prefix" {
  default = true
  type    = bool
}
variable "parameter_group_description" {
  default = null
  type    = string
}

##############################
# AWS IP Ranges Configuration
##############################
variable "aws_ip_ranges_regions" {
  type        = list(string)
  description = "List of AWS regions to include in the IP ranges data source"
  default     = ["us-east-1", "ap-southeast-2"]
}

variable "aws_ip_ranges_services" {
  type        = list(string)
  description = "List of AWS services to include in the IP ranges data source"
  default     = ["ec2", "lambda"]
}

##############################
# Eyecue/Custom
##############################
variable "eyecue_rds_stage_name" {
  default = "prod"
}
variable "eyecue_rds_customer_id" {
  type        = string
  description = "The Customer identifier (3 letters), like: MCD, KFC"
  default     = "nac"
}
variable "eyecue_rds_cloudflare_api_key" {
  type    = string
  default = ""
}
variable "eyecue_rds_null_replica_address" {
  type    = string
  default = "null.replica.rds.fingermark.tech"
}
variable "eyecue_rds_roles_allowed_to_read" {
  description = "The list of roles that are allowed to assume and read from the RDS instance"
  default     = ["AdminAccess", "DevAccess", "PowerAccess", "DeployerAccess"]
}
